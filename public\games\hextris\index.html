<!DOCTYPE html>
<html lang='en'>

<head>
	<title>Hextris</title>
	<meta name="description" content="An addictive puzzle game inspired by Tetris.">
	<link rel="manifest" href="manifest.webmanifest">
	<meta name="apple-mobile-web-app-title" content="Hextris">
	<meta name="apple-mobile-web-app-capable" content="yes">
	<link rel="apple-touch-icon" sizes="120x120" href="images/icons/apple-touch-120.png">
	<link rel="apple-touch-icon" sizes="152x152" href="images/icons/apple-touch-152.png">
	<link rel="apple-touch-icon" sizes="167x167" href="images/icons/apple-touch-167.png">
	<link rel="apple-touch-icon" sizes="180x180" href="images/icons/apple-touch-180.png">
	<link rel="apple-touch-icon" sizes="512x512" href="images/icons/apple-touch-512.png">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0, minimal-ui" />
	<meta property="og:title" content="Hextris" />
	<meta property="og:description" content="An addictive puzzle game inspired by Tetris." />
	<meta property="og:type" content="website" />
	<link rel="icon" type="image/png" href="favicon.ico">

	<link rel="stylesheet" href="style/fa/css/font-awesome.min.css">
	<link rel="stylesheet" type="text/css" href="style/style.css">
	<script type='text/javascript' src="vendor/hammer.min.js"></script>
	<script type='text/javascript' src="vendor/js.cookie.js"></script>
	<script type='text/javascript' src="vendor/jsonfn.min.js"></script>
	<script type='text/javascript' src="vendor/keypress.min.js"></script>
	<script type='text/javascript' src="vendor/jquery.js"></script>
	<script type='text/javascript' src="js/save-state.js"></script>
	<script type='text/javascript' src="js/view.js"></script>
	<script type='text/javascript' src="js/wavegen.js"></script>
	<script type='text/javascript' src="js/math.js"></script>
	<script type='text/javascript' src="js/Block.js"></script>
	<script type='text/javascript' src="js/Hex.js"></script>
	<script type='text/javascript' src="js/Text.js"></script>
	<script type='text/javascript' src="js/comboTimer.js"></script>
	<script type='text/javascript' src="js/checking.js"></script>
	<script type='text/javascript' src='js/update.js'></script>
	<script type='text/javascript' src='js/render.js'></script>
	<script type='text/javascript' src="js/input.js"></script>
	<script type='text/javascript' src="js/main.js"></script>
	<script type='text/javascript' src="js/initialization.js"></script>
	<script src="vendor/sweet-alert.min.js"></script>


</head>

<body>
	<canvas id="canvas"></canvas>
	<div id="overlay" class="faded overlay"></div>
	<div id='startBtn'></div>
	<div id="helpScreen" class='unselectable'>
		<div id='inst_main_body'></div>
	</div>
	<img id="openSideBar" class='helpText' src="./images/btn_help.svg" />
	<div class="faded overlay"></div>
	<img id="pauseBtn" src="./images/btn_pause.svg" />
	<img id='restartBtn' src="./images/btn_restart.svg" />

	<div id='highScoreInGameText'>
		<div id='highScoreInGameTextHeader'>HIGH SCORE</div>
		<div id='currentHighScore'>10292</div>
	</div>
	<div id="gameoverscreen">
		<div id='container'>
			<div id='gameOverBox' class='GOTitle'>GAME OVER</div>
			<div id='cScore'>1843</div>
			<div id='highScoresTitle' class='GOTitle'>HIGH SCORES</div>
			<div class='score'><span class='scoreNum'>1.</span>
				<div id="1place" style="display:inline;">0</div>
			</div>
			<div class='score'><span class='scoreNum'>2.</span>
				<div id="2place" style="display:inline;">0</div>
			</div>
			<div class='score'><span class='scoreNum'>3.</span>
				<div id="3place" style="display:inline;">0</div>
			</div>
		</div>
		<div id='bottomContainer'>
			<img id='restart' src='./images/btn_restart.svg' height='57px' />
		</div>
		<div id='buttonCont' style='display:none;'></div>
		<div id='socialShare' style='display:none;'></div>
		<div id='fork-ribbon' style='display:none;'></div>
		<div id='devtools' style='display:none;'></div>
		<div id='colorBlindBtn' style='display:none;'></div>
		<div id='clickToExit' style='display:none;'></div>
		<div id='devtoolsText' style='display:none;'></div>
		<div id='afterhr' style='display:none;'></div>
		<div id='score' style='display:none;'></div>
		<div id='title' style='display:none;'></div>
		<div id='highscores' style='display:none;'></div>
		<div id='navbar' style='display:none;'></div>
		<div id='tweet' style='display:none;'></div>
	</div>
	</div>

</body>

</html>